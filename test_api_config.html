<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #FF6B35;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover { background: #F7931E; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🦊 FOXAI API配置测试</h1>
        
        <div class="info">
            <strong>当前配置的服务器:</strong><br>
            <code id="apiUrl">http://***********:5678/webhook/flux</code>
        </div>
        
        <button onclick="testConnection()">测试服务器连接</button>
        
        <div id="result"></div>
    </div>

    <script>
        const API_URL = 'http://***********:5678/webhook/flux';
        
        async function testConnection() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">🔄 正在测试连接...</div>';
            
            try {
                const response = await fetch(`${API_URL}?prompt=test`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });
                
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="success">✅ 服务器连接成功！状态码: ' + response.status + '</div>';
                } else if (response.status === 400 || response.status === 500) {
                    resultDiv.innerHTML = '<div class="success">✅ 服务器在线（返回错误是正常的，因为这是测试请求）状态码: ' + response.status + '</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ 服务器响应异常，状态码: ' + response.status + '</div>';
                }
            } catch (error) {
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    resultDiv.innerHTML = '<div class="error">❌ 无法连接到服务器，可能的原因：<br>1. 服务器未运行<br>2. 网络连接问题<br>3. CORS跨域限制<br>4. 防火墙阻止</div>';
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ 连接错误: ' + error.message + '</div>';
                }
            }
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', testConnection);
    </script>
</body>
</html>
