{"name": "flux", "nodes": [{"parameters": {"path": "flux", "responseMode": "responseNode", "options": {}}, "id": "b851267c-ad34-49a2-b42c-a63d8bf55265", "name": "GET请求", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-60, 480], "webhookId": "a5a9106f-2389-4b29-8423-8c0d94717ed2"}, {"parameters": {"method": "POST", "url": "https://api.siliconflow.cn/v1/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpBearerAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"Qwen/Qwen2.5-7B-Instruct\",\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a professional AI image generation prompt optimizer. Please convert the user's simple description into a detailed, professional FLUX image generation prompt. Requirements: 1. Keep the original meaning unchanged 2. Add artistic style, lighting effects, composition details 3. Output in English 4. The prompt should be specific and expressive 5. Control the length between 100-200 words\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Please optimize this image description: {{ $('GET请求').item.json.query.prompt }}\"\n    }\n  ],\n  \"max_tokens\": 300,\n  \"temperature\": 0.7\n}", "options": {}}, "id": "prompt-enhancer", "name": "提示词优化", "type": "n8n-nodes-base.httpRequest", "position": [100, 480], "typeVersion": 4.2, "credentials": {"httpBearerAuth": {"id": "eIOKreIsFPd1CJQy", "name": "Bearer Auth account"}}, "onError": "continueErrorOutput"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "06e13542-cb9a-4a81-823e-07c913ba79f9", "leftValue": "={{ $('GET请求').item.json.query.seed }}", "rightValue": "", "operator": {"type": "string", "operation": "exists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [300, 480], "id": "382d2192-26db-4a13-9f35-6ff21c9d7ea8", "name": "Seed值有无"}, {"parameters": {"method": "POST", "url": "https://api.siliconflow.cn/v1/images/generations", "authentication": "genericCredentialType", "genericAuthType": "httpBearerAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"prompt_enhancement\": false,\n  \"prompt\": \"{{ $('提示词优化').item.json.choices[0].message.content }}\",\n  \"image_size\": \"{{ $ifEmpty($('GET请求').item.json.query.width,1024) }}x{{ $ifEmpty($('GET请求').item.json.query.height,1024) }}\",\n  \"model\": \"black-forest-labs/FLUX.1-dev\",\n  \"seed\": {{ $('GET请求').item.json.query.seed }}\n}", "options": {}}, "id": "76de81c8-f5a9-4f61-9321-f91a63c1ed21", "name": "调用 硅基流动 推理 API", "type": "n8n-nodes-base.httpRequest", "position": [500, 380], "notesInFlow": true, "typeVersion": 4.2, "retryOnFail": false, "credentials": {"httpBearerAuth": {"id": "eIOKreIsFPd1CJQy", "name": "Bearer Auth account"}}, "onError": "continueErrorOutput", "notes": "使用优化后的提示词"}, {"parameters": {"method": "POST", "url": "https://api.siliconflow.cn/v1/images/generations", "authentication": "genericCredentialType", "genericAuthType": "httpBearerAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"prompt_enhancement\": false,\n  \"prompt\": \"{{ $('提示词优化').item.json.choices[0].message.content }}\",\n  \"image_size\": \"{{ $ifEmpty($('GET请求').item.json.query.width,1024) }}x{{ $ifEmpty($('GET请求').item.json.query.height,1024) }}\",\n  \"model\": \"black-forest-labs/FLUX.1-dev\"\n}", "options": {}}, "id": "b5684a8e-46fb-4687-9445-ebbf6fdfc978", "name": "调用 硅基流动 推理 API1", "type": "n8n-nodes-base.httpRequest", "position": [500, 580], "notesInFlow": true, "typeVersion": 4.2, "retryOnFail": false, "credentials": {"httpBearerAuth": {"id": "eIOKreIsFPd1CJQy", "name": "Bearer Auth account"}}, "onError": "continueErrorOutput", "notes": "使用优化后的提示词"}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"originalPrompt\": \"{{ $('GET请求').item.json.query.prompt }}\",\n  \"enhancedPrompt\": \"{{ $('提示词优化').item.json.choices[0].message.content }}\",\n  \"imageUrl\": \"{{ $json.images[0].url }}\",\n  \"parameters\": {\n    \"width\": {{ $ifEmpty($('GET请求').item.json.query.width,1024) }},\n    \"height\": {{ $ifEmpty($('GET请求').item.json.query.height,1024) }},\n    \"seed\": {{ $ifEmpty($('GET请求').item.json.query.seed,'null') }},\n    \"model\": \"black-forest-labs/FLUX.1-dev\"\n  }\n}", "options": {}}, "id": "2ddb9610-15a2-4391-9dbf-22145bfbee25", "name": "成功响应", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [700, 380]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"originalPrompt\": \"{{ $('GET请求').item.json.query.prompt }}\",\n  \"enhancedPrompt\": \"{{ $('提示词优化').item.json.choices[0].message.content }}\",\n  \"imageUrl\": \"{{ $json.images[0].url }}\",\n  \"parameters\": {\n    \"width\": {{ $ifEmpty($('GET请求').item.json.query.width,1024) }},\n    \"height\": {{ $ifEmpty($('GET请求').item.json.query.height,1024) }},\n    \"seed\": null,\n    \"model\": \"black-forest-labs/FLUX.1-dev\"\n  }\n}", "options": {}}, "id": "10f661a3-dc44-4d7a-9feb-da921ca207c2", "name": "成功响应1", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [700, 580]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"error\": \"提示词优化失败：{{ $json.error.message || '未知错误' }}\",\n  \"step\": \"prompt_enhancement\"\n}", "options": {}}, "id": "prompt-error", "name": "提示词优化错误", "type": "n8n-nodes-base.respondToWebhook", "position": [300, 600], "typeVersion": 1.1}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"error\": \"硅基流动调用失败：{{ $json.error.message || '未知错误' }}\",\n  \"step\": \"image_generation\",\n  \"enhancedPrompt\": \"{{ $('提示词优化').item.json.choices[0].message.content }}\"\n}", "options": {}}, "id": "60feb16f-d4f6-40fc-96ab-ce36bef6109b", "name": "回复错误1", "type": "n8n-nodes-base.respondToWebhook", "position": [700, 280], "typeVersion": 1.1}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"error\": \"硅基流动调用失败：{{ $json.error.message || '未知错误' }}\",\n  \"step\": \"image_generation\",\n  \"enhancedPrompt\": \"{{ $('提示词优化').item.json.choices[0].message.content }}\"\n}", "options": {}}, "id": "b4da27a4-4e4b-407b-8190-f961a7025f10", "name": "回复错误", "type": "n8n-nodes-base.respondToWebhook", "position": [700, 680], "typeVersion": 1.1}, {"parameters": {"content": "## 增强版 FLUX 图像生成工作流\n\n新增功能：\n1. 智能提示词优化 - 使用 Qwen2.5-7B 模型\n2. JSON 响应格式 - 便于前端集成\n3. 完整错误处理 - 分步骤错误信息\n4. 优化后提示词返回 - 用户可查看优化结果\n\n配合前端网页使用，提供完整的图像生成体验。", "height": 160, "width": 400}, "id": "bbc5bcfa-5803-4cb7-b9d1-ae63cb288f19", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-60, 200]}], "pinData": {}, "connections": {"GET请求": {"main": [[{"node": "提示词优化", "type": "main", "index": 0}]]}, "提示词优化": {"main": [[{"node": "Seed值有无", "type": "main", "index": 0}], [{"node": "提示词优化错误", "type": "main", "index": 0}]]}, "Seed值有无": {"main": [[{"node": "调用 硅基流动 推理 API", "type": "main", "index": 0}], [{"node": "调用 硅基流动 推理 API1", "type": "main", "index": 0}]]}, "调用 硅基流动 推理 API": {"main": [[{"node": "成功响应", "type": "main", "index": 0}], [{"node": "回复错误1", "type": "main", "index": 0}]]}, "调用 硅基流动 推理 API1": {"main": [[{"node": "成功响应1", "type": "main", "index": 0}], [{"node": "回复错误", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "756f2410-34bf-4004-bdfa-6c58f7e5c85e", "meta": {"instanceId": "e11d00c2718992a58214a52e1f2f24c680abf6d1e56d9580c9a7fb8d725c63d8"}, "id": "psYBQPv4NpIaPNlE", "tags": []}