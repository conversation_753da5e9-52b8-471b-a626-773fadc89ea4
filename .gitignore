# FOXAI FLUX 项目 .gitignore

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 临时文件
*.tmp
*.temp
*.log

# 依赖包
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/
*.min.js
*.min.css

# 环境配置
.env
.env.local
.env.production
.env.test

# 缓存文件
.cache/
*.cache

# 备份文件
*.bak
*.backup
*.orig

# 用户生成的图像（如果有本地存储）
generated-images/
downloads/

# API密钥和敏感信息
config/secrets.json
api-keys.json
credentials.json 