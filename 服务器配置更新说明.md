# 🔄 服务器配置更新说明

## 📋 更新概述

根据您的要求，已成功更新服务器配置，实现了新旧服务器的备用机制。

### 🎯 更新目标
- 优先使用新服务器：`http://***********:5678/workflow/lskCjTsoomNhqBjq`
- 保留旧服务器作为备用：`http://************:5678`
- 实现自动故障转移机制

## 📝 具体更改

### 1. 主要配置文件更新

#### `flux-image-generator.html`
```javascript
// 更新前
const API_ENDPOINTS = [
    'http://************:5678/webhook/flux',
    'http://************:5678/webhook/a5a9106f-2389-4b29-8423-8c0d94717ed2',
    'http://************:5678/webhook-test/flux'
];

// 更新后
const API_ENDPOINTS = [
    'http://***********:5678/workflow/lskCjTsoomNhqBjq',  // 新服务器 - 优先使用
    'http://************:5678/webhook/flux',              // 旧服务器备用1
    'http://************:5678/webhook/a5a9106f-2389-4b29-8423-8c0d94717ed2', // 旧服务器备用2
    'http://************:5678/webhook-test/flux'          // 旧服务器备用3
];
```

### 2. 测试脚本更新

#### `test_commands.sh`
- 添加了新旧服务器的配置变量
- 实现了服务器连通性自动测试
- 更新了所有测试案例的URL

### 3. 文档更新

#### `部署说明.md`
- 更新了API端点配置示例
- 添加了备用服务器机制说明
- 详细说明了自动故障转移流程

#### `upgrade_guide.md`
- 更新了测试命令中的服务器地址

## 🔧 备用机制工作原理

### 自动故障转移流程
1. **优先连接**: 系统首先尝试连接新服务器
2. **故障检测**: 如果新服务器不可用，自动检测失败
3. **备用切换**: 按顺序尝试旧服务器的各个端点
4. **状态显示**: 界面显示当前使用的服务器状态

### 连接检测机制
- 页面加载时自动检测所有端点
- 实时显示服务器连接状态
- 支持HTTP 200、400、500状态码作为"在线"标识
- 5秒连接超时设置

## ✅ 测试结果

根据最新测试结果：
- ✅ 新服务器 (http://***********:5678) 连接正常
- ✅ 旧服务器 (http://************:5678) 连接正常
- ✅ 自动故障转移机制工作正常

## 🚀 使用说明

### 对用户的影响
- **无感知切换**: 用户无需手动选择服务器
- **提高可用性**: 双服务器备用提高系统可靠性
- **状态透明**: 界面清晰显示当前使用的服务器

### 管理员操作
1. 确保两个服务器的n8n工作流都已激活
2. 验证SiliconFlow API凭据在两个服务器上都正确配置
3. 定期检查服务器状态和日志

## 📊 配置优先级

1. **第一优先级**: `http://***********:5678/workflow/lskCjTsoomNhqBjq`
2. **第二优先级**: `http://************:5678/webhook/flux`
3. **第三优先级**: `http://************:5678/webhook/a5a9106f-2389-4b29-8423-8c0d94717ed2`
4. **第四优先级**: `http://************:5678/webhook-test/flux`

## 🔍 故障排除

### 如果新服务器不可用
- 系统会自动切换到旧服务器
- 界面会显示当前使用的服务器信息
- 用户可以正常使用图像生成功能

### 如果所有服务器都不可用
- 界面会显示"❌ FOXAI 所有端点都无法连接"
- 提供详细的故障排除提示
- 建议检查工作流激活状态和网络连接

## 📞 技术支持

如有问题，请检查：
1. n8n服务器是否正常运行
2. 工作流是否已激活
3. SiliconFlow API凭据是否正确
4. 网络连接和防火墙设置
